package com.sinoyd.lims.api.service.offline.strategy;

import com.sinoyd.lims.api.dto.vo.offLine.convert.OfflineConfigPackageVO;
import com.sinoyd.lims.api.dto.vo.offLine.convert.OfflineDataPackageVO;
import com.sinoyd.lims.api.dto.vo.offLine.convert.OnlineConfigPackageVO;

/**
 * 配置数据转换策略接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/09/23
 **/
public interface IConfigDataStrategy {

    /**
     * 转换填充配置数据离线数据包（全量下载）
     *
     * @param onlineConfig  在线配置数据包
     * @param offlineConfig 离线配置数据包
     */
    void convertFillOfflineConfig(OnlineConfigPackageVO onlineConfig, OfflineDataPackageVO offlineConfig);
}
