package com.sinoyd.lims.api.service.offline.strategy;

import com.sinoyd.lims.api.dto.vo.offLine.request.OfflineCheckVO;
import com.sinoyd.lims.api.dto.vo.offLine.convert.OfflineDataPackageVO;
import com.sinoyd.lims.api.dto.vo.offLine.convert.OnlineDataPackageVO;

import java.util.List;

/**
 * 业务数据双向转换策略接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/09/23
 **/
public interface IDataConversionStrategy {

    /**
     * 获取支持的表名
     *
     * @return 支持的表名
     */
    String getSupportedTableName();

    /**
     * 转换填充离线数据包（下载）
     *
     * @param onlineData  在线数据包
     * @param offlineData 离线数据包
     */
    void convertFillOfflineData(OnlineDataPackageVO onlineData, OfflineDataPackageVO offlineData);

    /**
     * 转换填充在线数据包（上传）
     *
     * @param onlineData 在线数据包
     * @param offlineData 离线数据包
     */
    void convertFillOnlineData(OnlineDataPackageVO onlineData, OfflineDataPackageVO offlineData);

    /**
     * 检测数据冲突
     *
     * @param onlineData  在线数据包
     * @param offlineData 离线数据包
     * @return 校验结果列表
     */
    List<OfflineCheckVO> check(OnlineDataPackageVO onlineData, OfflineDataPackageVO offlineData);
}
