package com.sinoyd.lims.api.service.offline.strategy.data;

import com.sinoyd.lims.api.dto.vo.offLine.convert.OfflineDataPackageVO;
import com.sinoyd.lims.api.dto.vo.offLine.convert.OnlineDataPackageVO;
import com.sinoyd.lims.api.dto.vo.offLine.request.OfflineCheckVO;
import com.sinoyd.lims.api.enums.EnumOfflineDataType;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 采样记录单数据转换策略
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/09/23
 **/
@Component
public class ConversionDataSamplingRecord extends AbstractDataConversionStrategy {

    /**
     * 获取支持的表名
     *
     * @return 支持的表名
     */
    @Override
    public String getSupportedTableName() {
        return EnumOfflineDataType.SAMPLE_RECORD.getTable();
    }

    /**
     * 转换填充离线数据包（下载）
     *
     * @param onlineData  在线数据包
     * @param offlineData 离线数据包
     */
    @Override
    public void convertFillOfflineData(OnlineDataPackageVO onlineData, OfflineDataPackageVO offlineData) {

    }

    /**
     * 离线数据转换为在线数据（上传）
     *
     * @param onlineData  在线数据包
     * @param offlineData 离线数据包
     */
    @Override
    public void convertFillOnlineData(OnlineDataPackageVO onlineData, OfflineDataPackageVO offlineData) {

    }

    /**
     * 检测数据冲突
     *
     * @param onlineData  在线数据包
     * @param offlineData 离线数据包
     * @return 校验结果列表
     */
    @Override
    public List<OfflineCheckVO> check(OnlineDataPackageVO onlineData, OfflineDataPackageVO offlineData) {
        return Collections.emptyList();
    }
}
