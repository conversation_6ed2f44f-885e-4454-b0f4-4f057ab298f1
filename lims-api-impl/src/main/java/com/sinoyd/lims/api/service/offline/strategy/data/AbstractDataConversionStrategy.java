package com.sinoyd.lims.api.service.offline.strategy.data;

import com.sinoyd.lims.api.dto.vo.offLine.convert.OfflineDataPackageVO;
import com.sinoyd.lims.api.dto.vo.offLine.convert.OnlineDataPackageVO;
import com.sinoyd.lims.api.dto.vo.offLine.request.OfflineCheckVO;
import com.sinoyd.lims.api.service.offline.strategy.IDataConversionStrategy;

import java.util.List;

/**
 * 业务数据转换抽象基类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/09/23
 **/
public abstract class AbstractDataConversionStrategy implements IDataConversionStrategy {

    /**
     * 获取支持的表名
     *
     * @return 支持的表名
     */
    public abstract String getSupportedTableName();

    /**
     * 转换填充离线数据包（下载）
     *
     * @param onlineData  在线数据包
     * @param offlineData 离线数据包
     */
    public abstract void convertFillOfflineData(OnlineDataPackageVO onlineData, OfflineDataPackageVO offlineData);

    /**
     * 离线数据转换为在线数据（上传）
     *
     * @param onlineData  在线数据包
     * @param offlineData 离线数据包
     */
    public abstract void convertFillOnlineData(OnlineDataPackageVO onlineData, OfflineDataPackageVO offlineData);

    /**
     * 检测数据冲突
     *
     * @param onlineData  在线数据包
     * @param offlineData 离线数据包
     * @return 校验结果列表
     */
    public abstract List<OfflineCheckVO> check(OnlineDataPackageVO onlineData, OfflineDataPackageVO offlineData);
}
