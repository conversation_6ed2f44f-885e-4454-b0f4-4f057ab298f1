package com.sinoyd.lims.api.service.offline.strategy.config;

import com.sinoyd.lims.api.dto.vo.offLine.convert.OfflineConfigPackageVO;
import com.sinoyd.lims.api.dto.vo.offLine.convert.OnlineConfigPackageVO;
import com.sinoyd.lims.api.service.offline.strategy.IConfigDataStrategy;

/**
 * 配置数据转换抽象基类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/09/23
 **/
public abstract class AbstractConfigDataStrategy implements IConfigDataStrategy {

    /**
     * 转换填充配置数据离线数据包
     *
     * @param onlineConfig  在线配置数据包
     * @param offlineConfig 离线配置数据包
     */
    public abstract void convertFillOfflineConfig(OnlineConfigPackageVO onlineConfig, OfflineConfigPackageVO offlineConfig);
}
