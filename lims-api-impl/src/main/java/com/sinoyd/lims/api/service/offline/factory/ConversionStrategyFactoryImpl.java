package com.sinoyd.lims.api.service.offline.factory;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.lims.api.dto.vo.offLine.convert.OfflineConfigPackageVO;
import com.sinoyd.lims.api.dto.vo.offLine.convert.OfflineDataPackageVO;
import com.sinoyd.lims.api.dto.vo.offLine.convert.OnlineConfigPackageVO;
import com.sinoyd.lims.api.dto.vo.offLine.convert.OnlineDataPackageVO;
import com.sinoyd.lims.api.dto.vo.offLine.request.OfflineDataDownloadRequestVO;
import com.sinoyd.lims.api.dto.vo.offLine.request.OfflineDataUploadRequestVO;
import com.sinoyd.lims.api.enums.EnumOfflineDataType;
import com.sinoyd.lims.api.service.offline.IConversionStrategyFactory;
import com.sinoyd.lims.api.service.offline.strategy.IConfigDataStrategy;
import com.sinoyd.lims.api.service.offline.strategy.IDataConversionStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 转换策略工厂实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/09/23
 **/
@Component
public class ConversionStrategyFactoryImpl implements IConversionStrategyFactory {

    /**
     * 业务数据转换策略映射
     */
    private Map<String, IDataConversionStrategy> dataStrategies;

    /**
     * 配置数据转换策略映射
     */
    private Map<String, IConfigDataStrategy> configStrategies;


    @Override
    public List<OfflineDataPackageVO> downloadOfflineData(OfflineDataDownloadRequestVO requestVO) {
        List<OfflineDataPackageVO> offlineDataList = new ArrayList<>();
        for (OnlineDataPackageVO onlineData : requestVO.getOnlineDataList()) {
            OfflineDataPackageVO offlineData = new OfflineDataPackageVO(onlineData);
            Arrays.stream(EnumOfflineDataType.values()).forEach(e ->
                    getDataConversionStrategy(e.getTable()).convertFillOfflineData(onlineData, offlineData));
            offlineDataList.add(offlineData);
        }
        return offlineDataList;
    }

    @Override
    public List<OfflineConfigPackageVO> downloadOfflineConfig(OfflineDataDownloadRequestVO requestVO) {
        List<OfflineConfigPackageVO> offlineDataList = new ArrayList<>();
        for (OnlineConfigPackageVO onlineData : requestVO.getOnlineConfigList()) {
            OfflineConfigPackageVO offlineConfig = new OfflineConfigPackageVO(onlineData);
            Arrays.stream(EnumOfflineDataType.values()).forEach(e ->
                    getConfigConversionStrategy(e.getTable()).convertFillOfflineConfig(onlineData, offlineConfig));
            offlineDataList.add(offlineConfig);
        }
        return offlineDataList;
    }

    @Override
    public List<OnlineDataPackageVO> uploadOfflineData(OfflineDataUploadRequestVO requestVO) {
        List<OnlineDataPackageVO> onlineData = new ArrayList<>();
        for (OfflineDataPackageVO offlineData : requestVO.getOfflineDataList()) {
            OnlineDataPackageVO onlinePackage = new OnlineDataPackageVO();
            Arrays.stream(EnumOfflineDataType.values()).forEach(e ->
                    getDataConversionStrategy(e.getTable()).convertFillOnlineData(onlinePackage, offlineData));
            onlineData.add(onlinePackage);
        }
        return onlineData;
    }

    /**
     * 获取业务数据转换策略
     *
     * @param tableName 表名
     * @return 业务数据转换策略
     */
    public IDataConversionStrategy getDataConversionStrategy(String tableName) {
        EnumOfflineDataType offlineDataType = EnumOfflineDataType.getEnumByTable(tableName);
        if (offlineDataType == null) {
            throw new BaseException("不支持的离线数据类型: " + tableName);
        }
        IDataConversionStrategy strategy = dataStrategies.get(offlineDataType.getBeanName());
        if (strategy == null) {
            throw new BaseException("未实现的离线业务数据转换策略: " + offlineDataType.getBeanName());
        }
        return strategy;
    }

    /**
     * 获取配置数据转换策略
     *
     * @param tableName 表名
     * @return 业务数据转换策略
     */
    public IConfigDataStrategy getConfigConversionStrategy(String tableName) {
        EnumOfflineDataType offlineDataType = EnumOfflineDataType.getEnumByTable(tableName);
        if (offlineDataType == null) {
            throw new BaseException("不支持的离线数据类型: " + tableName);
        }
        IConfigDataStrategy strategy = configStrategies.get(offlineDataType.getBeanName());
        if (strategy == null) {
            throw new BaseException("未实现的离线配置数据转换策略: " + offlineDataType.getBeanName());
        }
        return strategy;
    }

    @Autowired(required = false)
    public void setDataStrategies(Map<String, IDataConversionStrategy> dataStrategies) {
        this.dataStrategies = dataStrategies;
    }

    @Autowired(required = false)
    public void setConfigStrategies(Map<String, IConfigDataStrategy> configStrategies) {
        this.configStrategies = configStrategies;
    }
}
