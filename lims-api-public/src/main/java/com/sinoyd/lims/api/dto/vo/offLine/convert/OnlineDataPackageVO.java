package com.sinoyd.lims.api.dto.vo.offLine.convert;

import com.sinoyd.base.dto.lims.DtoDocument;
import com.sinoyd.lims.pro.dto.*;
import lombok.Data;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;

/**
 * 在线数据包
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/09/23
 **/
@Data
public class OnlineDataPackageVO {

    /**
     * 默认构造方法
     */
    public OnlineDataPackageVO() {
        this.createTime = new Date();
    }

    /**
     * 通过离线数据包构造
     *
     * @param offlineData 离线数据包
     */
    public OnlineDataPackageVO(OfflineDataPackageVO offlineData) {
        this.receiveId = offlineData.getReceiveId();
        this.orgId = offlineData.getOrgId();
        this.orgCode = offlineData.getOrgCode();
        this.createTime = new Date();
    }

    /**
     * 采样单id
     */
    private String receiveId;

    /**
     * 采样单
     */
    private DtoReceiveSampleRecord receiveSampleRecord;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 点位id集合
     */
    private Collection<String> folderIds = new ArrayList<>();

    /**
     * 点位集合
     */
    private Collection<DtoSampleFolder> sampleFolders;

    /**
     * 样品id集合
     */
    private Collection<String> sampleIds;

    /**
     * 样品集合
     */
    private Collection<DtoSample> samples;

    /**
     * 分析数据id集合
     */
    private Collection<String> analyzeDataIds;

    /**
     * 分析数据集合
     */
    private Collection<DtoAnalyseData> analyseDataList;

    /**
     * 样品分组id集合
     */
    private Collection<String> sampleGroupIds;

    /**
     * 样品分组集合
     */
    private Collection<DtoSampleGroup> sampleGroups;

    /**
     * 公共参数数据集合
     */
    private Collection<DtoParamsData> publicParamsDataList;

    /**
     * 点位参数数据集合
     */
    private Collection<DtoParamsData> folderParamsDataList;

    /**
     * 样品参数数据集合
     */
    private Collection<DtoParamsData> sampleParamsDataList;

    /**
     * 公式参数数据集合
     */
    private Collection<DtoAnalyseOriginalRecord> analyseOriginalRecords;

    /**
     * 文档数据集合
     */
    private Collection<DtoDocument> documents;


}
