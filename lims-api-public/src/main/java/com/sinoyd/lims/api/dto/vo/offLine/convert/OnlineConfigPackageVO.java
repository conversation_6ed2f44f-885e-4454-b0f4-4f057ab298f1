package com.sinoyd.lims.api.dto.vo.offLine.convert;

import com.sinoyd.base.dto.rcc.DtoSampleType;
import com.sinoyd.lims.lim.dto.lims.DtoPerson;
import com.sinoyd.lims.lim.dto.rcc.DtoSampleTypeGroup;
import com.sinoyd.lims.lim.dto.rcc.DtoSampleTypeGroup2Test;
import lombok.Data;

import java.util.Collection;
import java.util.Date;

/**
 * 在线配置数据包
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/09/23
 **/
@Data
public class OnlineConfigPackageVO {

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 机构编码
     */
    private String orgCode;

    // 4个配置表数据

    /**
     * 样品分组类型数据
     */
    private Collection<DtoSampleTypeGroup> sampleGroupTypes;

    /**
     * 样品分组类型测试项目数据
     */
    private Collection<DtoSampleTypeGroup2Test> sampleGroupTypeTests;

    /**
     * 样品类型数据
     */
    private Collection<DtoSampleType> sampleTypes;

    /**
     * 人员数据
     */
    private Collection<DtoPerson> persons;
}
