package com.sinoyd.lims.api.dto.vo.offLine.convert;

import com.sinoyd.lims.api.dto.vo.offLine.data.OffLinePersonVO;
import com.sinoyd.lims.api.dto.vo.offLine.data.OffLineSampleGroupTypeTestVO;
import com.sinoyd.lims.api.dto.vo.offLine.data.OffLineSampleGroupTypeVO;
import com.sinoyd.lims.api.dto.vo.offLine.data.OffLineSampleTypeVO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 离线配置数据包封装类VO
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/09/23
 **/
@Data
public class OfflineConfigPackageVO {

    /**
     * 默认构造方法
     */
    public OfflineConfigPackageVO() {
        this.createTime = new Date();
    }

    /**
     * 通过在线配置数据包构造
     *
     * @param onlineConfig 在线配置数据包
     */
    public OfflineConfigPackageVO(OnlineConfigPackageVO onlineConfig) {
        this.orgId = onlineConfig.getOrgId();
        this.orgCode = onlineConfig.getOrgCode();
        this.createTime = new Date();
    }

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 所属机构
     */
    private String orgId;

    /**
     * 机构编码
     */
    private String orgCode;

    // 4个配置表数据

    /**
     * 样品分组类型数据
     */
    private List<OffLineSampleGroupTypeVO> sampleGroupTypes;

    /**
     * 样品分组类型测试项目数据
     */
    private List<OffLineSampleGroupTypeTestVO> sampleGroupTypeTests;

    /**
     * 样品类型数据
     */
    private List<OffLineSampleTypeVO> sampleTypes;

    /**
     * 人员数据
     */
    private List<OffLinePersonVO> persons;
}
